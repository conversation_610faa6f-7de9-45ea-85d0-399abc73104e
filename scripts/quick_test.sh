#!/bin/bash

# Quick test script to verify multiline parser

set -e

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo "🧪 Quick Multiline Parser Test"
echo "=============================="
echo "Project directory: $PROJECT_DIR"

# Change to project directory
cd "$PROJECT_DIR"

# Create a small test log with your exact format
mkdir -p logs
cat > logs/quick_test.log << 'EOF'
[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Single line DEB log
[2025-09-30 13:59:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:47.6N +0530
        Min       = 1172 ns
        Max       = 820387 ns
        Mean      = 5900 ns
        Mode      = bucket 1
        CPU Time  = 423499 us
        Wall Time = 28298 ms

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      231 |     0.2% |         0.1%
           2-   4 us |    70943 |    70.9% |        53.8%

[2025-09-30 13:59:48.6N +0530][PRF][...c/base/src/reactor_epoll.cpp:635][D]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ------------------------------------------
                185             <USER>   <GROUP> socket id<20>
                  0          6919   Multicast socket id<21>

         Average TX Rate 0.00 Mbps
         Average RX Rate 28.73 Mbps

[2025-09-30 13:59:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Another single line DEB log
EOF

echo "📝 Created test log with your exact format"
echo ""

# Create a minimal config for testing
mkdir -p config db
cat > config/quick_test.yaml << 'EOF'
service:
  flush: 1
  parsers_file: fluent/config/parsers_multiline_fixed.yaml
  log_level: info

pipeline:
  inputs:
    - name: tail
      path: ./logs/quick_test.log
      read_from_head: true
      db: ./db/quick_test.db
      tag: test
      multiline.parser: multiline-prf-parser

  outputs:
    - name: stdout
      match: '*'
      format: json_lines
EOF

echo "⚙️  Created minimal test configuration"
echo ""

echo "🚀 Running Fluent Bit for 10 seconds..."
echo "Expected: 4 separate log records (1 DEB + 1 PRF multiline + 1 DEB)"
echo ""

# Run fluent-bit for 10 seconds
timeout 10s fluent-bit --config=config/quick_test.yaml || true

echo ""
echo "✅ Test completed!"
echo ""
echo "💡 What to look for:"
echo "   - PRF log should be ONE complete JSON record with all performance stats"
echo "   - DEB logs should be separate single-line records"
echo "   - No concatenation of different log types"

# Cleanup
rm -f logs/quick_test.log config/quick_test.yaml db/quick_test.db

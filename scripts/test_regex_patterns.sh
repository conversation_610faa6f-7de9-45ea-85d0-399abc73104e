#!/bin/bash

# Script to test regex patterns against actual log lines

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Regex Patterns Against Actual Log Lines${NC}"
echo ""

# Test log lines from your actual logs
TEST_LINES=(
    "[2025-09-30 13:59:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:"
    "[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request"
    "Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:47.6N +0530"
    "        Min       = 1172 ns"
    "        Max       = 820387 ns"
    "               Range |    Count |  Count % | Time Spent %"
    "        -------------+----------+----------+--------------"
    "           1-   2 us |      231 |     0.2% |         0.1%"
    ""
    "[2025-09-30 14:35:12.124868 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][D]"
    "        tx-bytes      rx-bytes      Description"
    "         Average TX Rate 0.00 Mbps"
)

# Test patterns
echo -e "${YELLOW}Testing Start Pattern (PRF logs):${NC}"
START_PATTERN='^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[PRF\].*'

for line in "${TEST_LINES[@]}"; do
    if echo "$line" | grep -qE "$START_PATTERN"; then
        echo -e "${GREEN}✅ MATCH:${NC} $line"
    else
        echo -e "   SKIP:  $line"
    fi
done

echo ""
echo -e "${YELLOW}Testing Continuation Pattern (exclude other log levels):${NC}"
CONT_PATTERN='^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[(DEB|INF|ERR|WAR|PRF)\]).*'

for line in "${TEST_LINES[@]}"; do
    if echo "$line" | grep -qE "$CONT_PATTERN"; then
        echo -e "${GREEN}✅ MATCH:${NC} $line"
    else
        echo -e "   SKIP:  $line"
    fi
done

echo ""
echo -e "${YELLOW}Testing Performance Data Pattern:${NC}"
PERF_PATTERN='^        .*|^Performance counter.*|^               Range.*|^        -.*|^           \d.*|^         Average.*|^$'

for line in "${TEST_LINES[@]}"; do
    if echo "$line" | grep -qE "$PERF_PATTERN"; then
        echo -e "${GREEN}✅ MATCH:${NC} $line"
    else
        echo -e "   SKIP:  $line"
    fi
done

echo ""
echo -e "${BLUE}📋 Pattern Analysis Summary:${NC}"
echo -e "${YELLOW}Your timestamp format:${NC} [2025-09-30 13:59:47.6N +0530]"
echo -e "${YELLOW}Key insight:${NC} The '.6N' part is not standard microseconds"
echo -e "${YELLOW}Solution:${NC} Using '\\w+' to match any word characters after the dot"

echo ""
echo -e "${GREEN}✅ Updated patterns should now work correctly!${NC}"

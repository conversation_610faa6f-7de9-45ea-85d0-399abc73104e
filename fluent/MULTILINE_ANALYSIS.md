# Multiline Parser Analysis and Fixes

## 🔍 Issues Found in Your Original Configuration

### 1. **Parser Application Scope**
**Problem:** The multiline parser `multiline-regex-test` is only applied to the `qcore_broker` input:

<augment_code_snippet path="fluent-bit.yaml" mode="EXCERPT">
````yaml
- name: tail
  path: /app/qrt_sup/qcore/logs/*broker*.log
  # ... other config ...
  tag: qcore_broker
  multiline.parser: multiline-regex-test
````
</augment_code_snippet>

**Impact:** If your sample logs are from a different source (not broker logs), the multiline parser won't be applied.

**Fix:** Apply the parser to the correct input source or all relevant inputs.

### 2. **Continuation Regex Pattern**
**Problem:** The continuation pattern is too broad:

<augment_code_snippet path="parsers_multiline.yaml" mode="EXCERPT">
````yaml
- state: cont
  regex: '^(?!\[\d{4}-\d{2}-\d{2} ).*'
  next_state: cont
````
</augment_code_snippet>

**Issue:** This pattern only checks for the date part, not the full timestamp format. It might incorrectly capture lines that start with dates but aren't actual log entries.

**Fix:** Use the complete timestamp pattern:
```yaml
regex: '^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]).*'
```

### 3. **Log Format Analysis**
Looking at your sample logs, the multiline entries follow this pattern:

```
[2025-09-30 14:35:12.124740 +0800][PRF][...../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:12.124749672 +0800
        Min       = 1877 ns
        Max       = 833798 ns
        Mean      = 3995 ns
        Mode      = bucket 2
        CPU Time  = 399546 us
        Wall Time = 27022 ms
        Max Time  = 2025-09-30 14:34:50.253648410 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      231 |     0.2% |         0.1%
           2-   4 us |    70943 |    70.9% |        53.8%
           4-   8 us |    25850 |    25.9% |        34.5%
           8-  16 us |     2548 |     2.5% |         6.9%
          16-  32 us |      364 |     0.4% |         1.8%
          32-  65 us |       21 |     0.0% |         0.2%
          65- 131 us |       17 |     0.0% |         0.4%
         131- 262 us |       14 |     0.0% |         0.8%
         262- 524 us |        8 |     0.0% |         0.8%
         524-1048 us |        4 |     0.0% |         0.7%

[2025-09-30 14:35:12.124868 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][D]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                185             <USER>   <GROUP> socket id<20> Send Target: <*************:17031> Interface: <***********:0>
                  0          6919   Multicast socket id<21> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
               1065             0   Multicast socket id<26> Send Target: <*************:17032> Interface: <***********:0>
                  0      97049920   Multicast socket id<7> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.00 Mbps
         Average RX Rate 28.73 Mbps
```

**Key Observations:**
- Multiline records start with `[timestamp][PRF]...`
- They contain performance statistics and network statistics
- Some PRF entries have multiple sections (performance stats + network stats)
- Each section might start with a new `[timestamp][PRF]` line

## 🔧 Recommended Fixes

### Fix 1: Improved Basic Parser
```yaml
multiline_parsers:
  - name: multiline-regex-fixed
    type: regex
    flush_timeout: 5000
    
    rules:
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
        next_state: cont
      
      - state: cont
        regex: '^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]).*'
        next_state: cont
```

### Fix 2: More Specific Parser
```yaml
multiline_parsers:
  - name: multiline-regex-specific
    type: regex
    flush_timeout: 5000
    
    rules:
      # Start with Performance Stats
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*Performance Stats:'
        next_state: perf_stats
      
      # Performance stats lines
      - state: perf_stats
        regex: '^(Performance counter|        |               Range |        ---|           \d|         Average|$)'
        next_state: perf_stats
      
      # Network stats section (new PRF line)
      - state: perf_stats
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
        next_state: network_stats
      
      # Network stats lines
      - state: network_stats
        regex: '^(        tx-bytes|        ---|                \d|         Average|$)'
        next_state: network_stats
```

### Fix 3: Configuration Application
Apply the parser to the correct input:

```yaml
- name: tail
  path: /path/to/your/actual/log/files/*.log
  # ... other config ...
  multiline.parser: multiline-regex-fixed
```

## 🧪 Testing Strategy

1. **Start Simple:** Use the basic fixed parser first
2. **Test with Sample Data:** Use the provided test environment
3. **Monitor Output:** Check if multiline records are properly assembled
4. **Iterate:** Refine the parser based on actual log patterns
5. **Validate:** Ensure logs appear correctly in Coralogix

## 🚨 Common Pitfalls

1. **Regex Escaping:** YAML requires proper escaping of special characters
2. **State Transitions:** Ensure state machine logic is correct
3. **Timeout Values:** Adjust `flush_timeout` based on log frequency
4. **Performance:** Complex regex patterns can impact performance
5. **Testing:** Always test with real log data, not just samples

## 📊 Expected Behavior

With the fixed parser, you should see:
- Complete performance statistics as single log entries
- Network statistics grouped with their corresponding performance stats
- Proper JSON formatting in Fluent Bit output
- Correct multiline record assembly in Coralogix

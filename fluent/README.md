# Fluent Bit Multiline Parser Test Environment

This directory contains a complete test environment for debugging and testing Fluent Bit multiline parsers on macOS.

## 🚀 Quick Start

1. **Install Fluent Bit and setup environment:**
   ```bash
   cd fluent/scripts
   chmod +x *.sh
   ./install_fluentbit.sh
   ```

2. **Update Coralogix API Key:**
   Edit `config/fluent-bit-test.yaml` and replace `YOUR_CORALOGIX_API_KEY_HERE` with your actual Coralogix API key.

3. **Test the multiline parser:**
   ```bash
   ./test_multiline.sh
   ```

4. **Run Fluent Bit continuously:**
   ```bash
   ./run_fluentbit.sh
   ```

5. **Generate test logs (in another terminal):**
   ```bash
   ./generate_logs.sh
   ```

## 📁 Directory Structure

```
fluent/
├── config/
│   ├── fluent-bit-test.yaml          # Main Fluent Bit configuration
│   └── parsers_multiline_fixed.yaml  # Fixed multiline parsers
├── scripts/
│   ├── install_fluentbit.sh          # Installation script
│   ├── run_fluentbit.sh              # Run Fluent Bit
│   ├── test_multiline.sh             # Test multiline parsing
│   ├── generate_logs.sh              # Generate test logs
│   └── verify_setup.sh               # Verify environment setup
├── logs/
│   └── test.log                      # Sample log file
├── storage/                          # Fluent Bit storage directory
├── db/                              # Database files directory
├── README.md                         # This file
└── MULTILINE_ANALYSIS.md            # Detailed analysis of parser issues
```

## 🔧 Configuration Files

### Main Configuration (`fluent-bit-test.yaml`)
- Simplified version of your original config
- Focuses on testing multiline parsing
- Outputs to both console and Coralogix
- Uses debug logging for troubleshooting

### Fixed Multiline Parser (`parsers_multiline_fixed.yaml`)
- **multiline-regex-fixed**: Improved version of your original parser
- **multiline-regex-specific**: Alternative parser with more specific patterns

## 🐛 Issues Found in Original Configuration

### 1. Regex Pattern Issues
**Original Problem:**
```yaml
regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
```

**Fixed Version:**
```yaml
regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
```
The pattern was correct, but the continuation pattern needed improvement.

### 2. Continuation Pattern Issues
**Original Problem:**
```yaml
regex: '^(?!\[\d{4}-\d{2}-\d{2} ).*'
```

**Fixed Version:**
```yaml
regex: '^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]).*'
```
The continuation pattern now matches the full timestamp format to avoid false positives.

### 3. Parser Application
**Issue:** The multiline parser was only applied to `qcore_broker` logs, but your sample logs might be from different sources.

**Solution:** Apply the parser to the appropriate input source or test with the correct log files.

## 🧪 Testing Multiline Parsing

### How Multiline Parsers Work

1. **State Machine**: Uses states to track multiline record assembly
2. **Start State**: Identifies the beginning of a multiline record
3. **Continuation States**: Captures subsequent lines belonging to the same record
4. **Flush Timeout**: Determines when to flush incomplete records

### Your Log Pattern Analysis

Looking at your sample logs, the multiline records follow this pattern:

```
[2025-09-30 14:35:12.124740 +0800][PRF][...../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:12.124749672 +0800
        Min       = 1877 ns
        Max       = 833798 ns
        Mean      = 3995 ns
        ...
```

The parser should:
1. Start when it sees `[timestamp][PRF]...Performance Stats:`
2. Continue collecting lines until it sees another `[timestamp]` line
3. Flush the complete multiline record

## 🔍 Debugging Tips

1. **Enable Debug Logging:**
   ```yaml
   service:
     log_level: debug
   ```

2. **Use Console Output:**
   ```yaml
   outputs:
     - name: stdout
       match: '*'
       format: json_lines
   ```

3. **Check Parser Syntax:**
   ```bash
   fluent-bit --dry-run --config=config/fluent-bit-test.yaml
   ```

4. **Monitor Log Processing:**
   ```bash
   tail -f logs/test.log
   ```

## 📊 Expected Results

When the multiline parser works correctly, you should see:
- Complete performance stats blocks as single log entries
- Proper JSON formatting in the output
- All related lines grouped together in Coralogix

## 🚨 Troubleshooting

### Common Issues:
1. **Parser not applied**: Check that the input has `multiline.parser` configured
2. **Regex not matching**: Test regex patterns with sample log lines
3. **Timeout issues**: Adjust `flush_timeout` value
4. **State transitions**: Verify state machine logic

### Verification Steps:
1. Check Fluent Bit logs for parser errors
2. Verify regex patterns match your log format
3. Ensure parser file is loaded correctly
4. Test with simplified log samples

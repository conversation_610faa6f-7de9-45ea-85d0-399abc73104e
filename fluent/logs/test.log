[2025-09-30 14:34:55.862692 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515377]
[2025-09-30 14:34:55.862702 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515378]
[2025-09-30 14:34:55.862712 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515379]
[2025-09-30 14:34:55.862722 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515380]
[2025-09-30 14:34:55.862734 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515381]
[2025-09-30 14:34:55.862744 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515382]
[2025-09-30 14:35:12.124740 +0800][PRF][...../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:12.124749672 +0800
        Min       = 1877 ns
        Max       = 833798 ns
        Mean      = 3995 ns
        Mode      = bucket 2
        CPU Time  = 399546 us
        Wall Time = 27022 ms
        Max Time  = 2025-09-30 14:34:50.253648410 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      231 |     0.2% |         0.1%
           2-   4 us |    70943 |    70.9% |        53.8%
           4-   8 us |    25850 |    25.9% |        34.5%
           8-  16 us |     2548 |     2.5% |         6.9%
          16-  32 us |      364 |     0.4% |         1.8%
          32-  65 us |       21 |     0.0% |         0.2%
          65- 131 us |       17 |     0.0% |         0.4%
         131- 262 us |       14 |     0.0% |         0.8%
         262- 524 us |        8 |     0.0% |         0.8%
         524-1048 us |        4 |     0.0% |         0.7%

[2025-09-30 14:35:12.124868 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][D]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                185             <USER>   <GROUP> socket id<20> Send Target: <*************:17031> Interface: <***********:0>
                  0          6919   Multicast socket id<21> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
               1065             0   Multicast socket id<26> Send Target: <*************:17032> Interface: <***********:0>
                  0      97049920   Multicast socket id<7> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.00 Mbps
         Average RX Rate 28.73 Mbps

[2025-09-30 14:35:17.244776 +0800][PRF][...../src/base/src/profiler.cpp:139][H] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:17.244781921 +0800
        Min       = 1545 ns
        Max       = 1018968 ns
        Mean      = 5261 ns
        Mode      = bucket 2
        CPU Time  = 526191 us
        Wall Time = 27855 ms
        Max Time  = 2025-09-30 14:34:50.253834591 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      143 |     0.1% |         0.1%
           2-   4 us |    56868 |    56.9% |        32.0%
           4-   8 us |    29609 |    29.6% |        32.5%
           8-  16 us |    11288 |    11.3% |        23.4%
          16-  32 us |     1835 |     1.8% |         7.5%
          32-  65 us |      166 |     0.2% |         1.2%
          65- 131 us |       59 |     0.1% |         1.1%
         131- 262 us |       15 |     0.0% |         0.6%
         262- 524 us |       12 |     0.0% |         0.9%
         524-1048 us |        5 |     0.0% |         0.7%

[2025-09-30 14:35:17.244885 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][H]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                222             <USER>   <GROUP> socket id<31> Send Target: <*************:17031> Interface: <***********:0>
                  0          7400   Multicast socket id<32> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
              41137           185   Multicast socket id<33> Send Target: <*************:17032> Interface: <***********:0>
                  0      97349992   Multicast socket id<34> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.01 Mbps
         Average RX Rate 27.96 Mbps
[2025-09-30 13:53:45.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:53:45.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:57:00.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:57:00.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [2516]
[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [3425]
[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4333]
[2025-09-30 13:57:10.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:57:10.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [2975]
[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [3884]
[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4792]
[2025-09-30 13:59:16.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:16.6N +0530
        Min       = 2267 ns
        Max       = 865887 ns
        Mean      = 4378 ns
        Mode      = bucket 3
        CPU Time  = 303207 us
        Wall Time = 26036 ms

[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515425]
[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515393]
[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515094]
[2025-09-30 13:59:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:27.6N +0530
        Min       = 2829 ns
        Max       = 836375 ns
        Mean      = 3489 ns
        Mode      = bucket 2
        CPU Time  = 449953 us
        Wall Time = 29762 ms

[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515654]
[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515573]
[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515587]
[2025-09-30 13:59:37.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:37.6N +0530
        Min       = 4093 ns
        Max       = 1039852 ns
        Mean      = 3211 ns
        Mode      = bucket 2
        CPU Time  = 536133 us
        Wall Time = 25562 ms

[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515726]
[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515499]
[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515820]
[2025-09-30 13:59:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:47.6N +0530
        Min       = 1172 ns
        Max       = 820387 ns
        Mean      = 5900 ns
        Mode      = bucket 1
        CPU Time  = 423499 us
        Wall Time = 28298 ms

[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515849]
[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515583]
[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515592]
[2025-09-30 13:59:57.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:57.6N +0530
        Min       = 3061 ns
        Max       = 814474 ns
        Mean      = 5351 ns
        Mode      = bucket 1
        CPU Time  = 597656 us
        Wall Time = 29196 ms

[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515025]
[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515249]
[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515386]
[2025-09-30 14:00:07.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:07.6N +0530
        Min       = 3722 ns
        Max       = 841887 ns
        Mean      = 4545 ns
        Mode      = bucket 4
        CPU Time  = 321999 us
        Wall Time = 29175 ms

[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515382]
[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515581]
[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515412]
[2025-09-30 14:00:17.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:17.6N +0530
        Min       = 4298 ns
        Max       = 1109787 ns
        Mean      = 5850 ns
        Mode      = bucket 3
        CPU Time  = 472984 us
        Wall Time = 27143 ms

[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515624]
[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515069]
[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515707]
[2025-09-30 14:00:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:27.6N +0530
        Min       = 2410 ns
        Max       = 1151325 ns
        Mean      = 4425 ns
        Mode      = bucket 2
        CPU Time  = 425228 us
        Wall Time = 26100 ms

[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515511]
[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515105]
[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515222]
[2025-09-30 14:00:37.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:37.6N +0530
        Min       = 2832 ns
        Max       = 1031704 ns
        Mean      = 4059 ns
        Mode      = bucket 2
        CPU Time  = 595930 us
        Wall Time = 25760 ms

[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515025]
[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515144]
[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515024]
[2025-09-30 14:00:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:47.6N +0530
        Min       = 1713 ns
        Max       = 1175885 ns
        Mean      = 3570 ns
        Mode      = bucket 1
        CPU Time  = 491542 us
        Wall Time = 27023 ms

[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515225]
[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515005]
[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515166]
[2025-09-30 14:00:57.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:57.6N +0530
        Min       = 4531 ns
        Max       = 1079650 ns
        Mean      = 5990 ns
        Mode      = bucket 4
        CPU Time  = 515542 us
        Wall Time = 29445 ms

[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515860]
[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515601]
[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515891]
[2025-09-30 14:01:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:08.6N +0530
        Min       = 2170 ns
        Max       = 1034747 ns
        Mean      = 4614 ns
        Mode      = bucket 5
        CPU Time  = 454181 us
        Wall Time = 28410 ms

[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515668]
[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515959]
[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515822]
[2025-09-30 14:01:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:18.6N +0530
        Min       = 2718 ns
        Max       = 1179328 ns
        Mean      = 4024 ns
        Mode      = bucket 2
        CPU Time  = 415346 us
        Wall Time = 26729 ms

[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515749]
[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515353]
[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515868]
[2025-09-30 14:01:28.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:28.6N +0530
        Min       = 3360 ns
        Max       = 815242 ns
        Mean      = 5826 ns
        Mode      = bucket 3
        CPU Time  = 471686 us
        Wall Time = 28816 ms

[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515004]
[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515875]
[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515001]
[2025-09-30 14:01:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:38.6N +0530
        Min       = 2953 ns
        Max       = 1179711 ns
        Mean      = 3032 ns
        Mode      = bucket 2
        CPU Time  = 480560 us
        Wall Time = 29740 ms

[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515157]
[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515115]
[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515689]
[2025-09-30 14:01:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:48.6N +0530
        Min       = 4678 ns
        Max       = 944850 ns
        Mean      = 5416 ns
        Mode      = bucket 1
        CPU Time  = 499782 us
        Wall Time = 26698 ms

[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515628]
[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515794]
[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515771]
[2025-09-30 14:01:58.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:58.6N +0530
        Min       = 3367 ns
        Max       = 1085133 ns
        Mean      = 5779 ns
        Mode      = bucket 4
        CPU Time  = 332305 us
        Wall Time = 25455 ms

[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515248]
[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515411]
[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515739]
[2025-09-30 14:02:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:08.6N +0530
        Min       = 1213 ns
        Max       = 800299 ns
        Mean      = 4323 ns
        Mode      = bucket 5
        CPU Time  = 516343 us
        Wall Time = 29203 ms

[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515344]
[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515107]
[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515959]
[2025-09-30 14:02:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:18.6N +0530
        Min       = 1125 ns
        Max       = 1130676 ns
        Mean      = 3989 ns
        Mode      = bucket 1
        CPU Time  = 342394 us
        Wall Time = 29206 ms

[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515916]
[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515014]
[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515983]
[2025-09-30 14:02:28.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:28.6N +0530
        Min       = 2764 ns
        Max       = 1185052 ns
        Mean      = 5922 ns
        Mode      = bucket 1
        CPU Time  = 559319 us
        Wall Time = 28346 ms

[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515151]
[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515429]
[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515157]
[2025-09-30 14:02:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:38.6N +0530
        Min       = 3826 ns
        Max       = 1045168 ns
        Mean      = 4891 ns
        Mode      = bucket 1
        CPU Time  = 554065 us
        Wall Time = 28404 ms

[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515671]
[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515328]
[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515621]
[2025-09-30 14:02:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:48.6N +0530
        Min       = 3871 ns
        Max       = 851163 ns
        Mean      = 4327 ns
        Mode      = bucket 2
        CPU Time  = 572567 us
        Wall Time = 29525 ms

[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515231]
[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515812]
[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515692]
[2025-09-30 14:02:58.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:58.6N +0530
        Min       = 3311 ns
        Max       = 836189 ns
        Mean      = 4259 ns
        Mode      = bucket 5
        CPU Time  = 434169 us
        Wall Time = 26156 ms

[2025-09-30 14:02:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515642]
[2025-09-30 14:02:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515264]
[2025-09-30 14:02:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515888]
[2025-09-30 14:03:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:08.6N +0530
        Min       = 3341 ns
        Max       = 843066 ns
        Mean      = 3245 ns
        Mode      = bucket 4
        CPU Time  = 518167 us
        Wall Time = 29217 ms

[2025-09-30 14:03:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515303]
[2025-09-30 14:03:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515274]
[2025-09-30 14:03:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515261]
[2025-09-30 14:03:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:18.6N +0530
        Min       = 4574 ns
        Max       = 805966 ns
        Mean      = 3901 ns
        Mode      = bucket 1
        CPU Time  = 440852 us
        Wall Time = 29111 ms

[2025-09-30 14:03:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515041]
[2025-09-30 14:03:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515811]
[2025-09-30 14:03:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515990]
[2025-09-30 14:03:28.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:28.6N +0530
        Min       = 3452 ns
        Max       = 935067 ns
        Mean      = 3540 ns
        Mode      = bucket 3
        CPU Time  = 321399 us
        Wall Time = 29859 ms

[2025-09-30 14:03:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515420]
[2025-09-30 14:03:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515789]
[2025-09-30 14:03:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515638]
[2025-09-30 14:03:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:38.6N +0530
        Min       = 1741 ns
        Max       = 1055150 ns
        Mean      = 5701 ns
        Mode      = bucket 3
        CPU Time  = 383315 us
        Wall Time = 29014 ms

[2025-09-30 14:03:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515802]
[2025-09-30 14:03:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515095]
[2025-09-30 14:03:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515870]
[2025-09-30 14:03:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:48.6N +0530
        Min       = 3204 ns
        Max       = 1164990 ns
        Mean      = 3647 ns
        Mode      = bucket 1
        CPU Time  = 347634 us
        Wall Time = 28142 ms

[2025-09-30 14:03:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515119]
[2025-09-30 14:03:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515145]
[2025-09-30 14:03:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515074]
[2025-09-30 14:03:59.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:03:59.6N +0530
        Min       = 4868 ns
        Max       = 984591 ns
        Mean      = 4150 ns
        Mode      = bucket 2
        CPU Time  = 588002 us
        Wall Time = 26977 ms

[2025-09-30 14:03:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515756]
[2025-09-30 14:03:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515059]
[2025-09-30 14:03:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515308]
[2025-09-30 14:04:09.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:09.6N +0530
        Min       = 2834 ns
        Max       = 852880 ns
        Mean      = 4338 ns
        Mode      = bucket 2
        CPU Time  = 369733 us
        Wall Time = 26673 ms

[2025-09-30 14:04:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515740]
[2025-09-30 14:04:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515884]
[2025-09-30 14:04:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515118]
[2025-09-30 14:04:19.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:19.6N +0530
        Min       = 4699 ns
        Max       = 1021159 ns
        Mean      = 4359 ns
        Mode      = bucket 3
        CPU Time  = 365801 us
        Wall Time = 27868 ms

[2025-09-30 14:04:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515395]
[2025-09-30 14:04:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515163]
[2025-09-30 14:04:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515233]
[2025-09-30 14:04:29.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:29.6N +0530
        Min       = 2440 ns
        Max       = 875589 ns
        Mean      = 5049 ns
        Mode      = bucket 5
        CPU Time  = 436003 us
        Wall Time = 26210 ms

[2025-09-30 14:04:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515534]
[2025-09-30 14:04:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515406]
[2025-09-30 14:04:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515000]
[2025-09-30 14:04:39.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:39.6N +0530
        Min       = 4661 ns
        Max       = 992045 ns
        Mean      = 5307 ns
        Mode      = bucket 4
        CPU Time  = 349784 us
        Wall Time = 27352 ms

[2025-09-30 14:04:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515351]
[2025-09-30 14:04:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515778]
[2025-09-30 14:04:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515754]
[2025-09-30 14:04:49.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:49.6N +0530
        Min       = 2027 ns
        Max       = 1050879 ns
        Mean      = 5040 ns
        Mode      = bucket 1
        CPU Time  = 546383 us
        Wall Time = 27241 ms

[2025-09-30 14:04:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515726]
[2025-09-30 14:04:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515765]
[2025-09-30 14:04:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515869]
[2025-09-30 14:04:59.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:04:59.6N +0530
        Min       = 4941 ns
        Max       = 1132082 ns
        Mean      = 5507 ns
        Mode      = bucket 2
        CPU Time  = 307826 us
        Wall Time = 29216 ms

[2025-09-30 14:04:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515895]
[2025-09-30 14:04:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515816]
[2025-09-30 14:04:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515795]
[2025-09-30 14:05:09.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:09.6N +0530
        Min       = 3590 ns
        Max       = 943698 ns
        Mean      = 3700 ns
        Mode      = bucket 2
        CPU Time  = 582380 us
        Wall Time = 26540 ms

[2025-09-30 14:05:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515711]
[2025-09-30 14:05:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515612]
[2025-09-30 14:05:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515198]
[2025-09-30 14:05:19.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:19.6N +0530
        Min       = 3746 ns
        Max       = 1146236 ns
        Mean      = 3517 ns
        Mode      = bucket 1
        CPU Time  = 310775 us
        Wall Time = 26066 ms

[2025-09-30 14:05:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515729]
[2025-09-30 14:05:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515011]
[2025-09-30 14:05:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515540]
[2025-09-30 14:05:29.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:29.6N +0530
        Min       = 4884 ns
        Max       = 1149530 ns
        Mean      = 4765 ns
        Mode      = bucket 3
        CPU Time  = 591144 us
        Wall Time = 25307 ms

[2025-09-30 14:05:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515228]
[2025-09-30 14:05:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515646]
[2025-09-30 14:05:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515419]
[2025-09-30 14:05:39.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:39.6N +0530
        Min       = 2443 ns
        Max       = 1081806 ns
        Mean      = 5634 ns
        Mode      = bucket 5
        CPU Time  = 313786 us
        Wall Time = 28441 ms

[2025-09-30 14:05:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515398]
[2025-09-30 14:05:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515606]
[2025-09-30 14:05:39.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515227]
[2025-09-30 14:05:49.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:49.6N +0530
        Min       = 2391 ns
        Max       = 1171055 ns
        Mean      = 3379 ns
        Mode      = bucket 1
        CPU Time  = 415956 us
        Wall Time = 28365 ms

[2025-09-30 14:05:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515553]
[2025-09-30 14:05:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515163]
[2025-09-30 14:05:49.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515916]
[2025-09-30 14:05:59.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:05:59.6N +0530
        Min       = 1750 ns
        Max       = 1006756 ns
        Mean      = 3573 ns
        Mode      = bucket 5
        CPU Time  = 463320 us
        Wall Time = 28128 ms

[2025-09-30 14:05:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515110]
[2025-09-30 14:05:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515808]
[2025-09-30 14:05:59.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515193]
[2025-09-30 14:06:09.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:06:09.6N +0530
        Min       = 4615 ns
        Max       = 807058 ns
        Mean      = 3585 ns
        Mode      = bucket 3
        CPU Time  = 451606 us
        Wall Time = 26881 ms

[2025-09-30 14:06:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515930]
[2025-09-30 14:06:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515399]
[2025-09-30 14:06:09.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515836]
[2025-09-30 14:06:19.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:06:19.6N +0530
        Min       = 1182 ns
        Max       = 1085182 ns
        Mean      = 4157 ns
        Mode      = bucket 4
        CPU Time  = 354476 us
        Wall Time = 25145 ms

[2025-09-30 14:06:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515758]
[2025-09-30 14:06:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515321]
[2025-09-30 14:06:19.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515262]
[2025-09-30 14:06:29.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:06:29.6N +0530
        Min       = 1554 ns
        Max       = 1009836 ns
        Mean      = 5451 ns
        Mode      = bucket 4
        CPU Time  = 430102 us
        Wall Time = 25333 ms

[2025-09-30 14:06:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515469]
[2025-09-30 14:06:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515372]
[2025-09-30 14:06:29.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515054]
[2025-09-30 14:06:39.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:06:39.6N +0530
        Min       = 3499 ns
        Max       = 917447 ns
        Mean      = 5619 ns
        Mode      = bucket 3
        CPU Time  = 569467 us
        Wall Time = 25738 ms

[2025-09-30 14:06:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515448]
[2025-09-30 14:06:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515018]
[2025-09-30 14:06:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515200]
[2025-09-30 14:06:50.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:06:50.6N +0530
        Min       = 4581 ns
        Max       = 1185533 ns
        Mean      = 4724 ns
        Mode      = bucket 4
        CPU Time  = 351709 us
        Wall Time = 27418 ms

[2025-09-30 14:06:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515961]
[2025-09-30 14:06:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515945]
[2025-09-30 14:06:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515245]
[2025-09-30 14:07:00.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:00.6N +0530
        Min       = 4688 ns
        Max       = 1096466 ns
        Mean      = 4536 ns
        Mode      = bucket 1
        CPU Time  = 401952 us
        Wall Time = 25470 ms

[2025-09-30 14:07:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515247]
[2025-09-30 14:07:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515066]
[2025-09-30 14:07:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515796]
[2025-09-30 14:07:10.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:10.6N +0530
        Min       = 3493 ns
        Max       = 1171816 ns
        Mean      = 5050 ns
        Mode      = bucket 2
        CPU Time  = 522728 us
        Wall Time = 27021 ms

[2025-09-30 14:07:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515882]
[2025-09-30 14:07:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515286]
[2025-09-30 14:07:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515974]
[2025-09-30 14:07:20.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:20.6N +0530
        Min       = 3943 ns
        Max       = 1046630 ns
        Mean      = 3348 ns
        Mode      = bucket 1
        CPU Time  = 346280 us
        Wall Time = 27696 ms

[2025-09-30 14:07:20.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515956]
[2025-09-30 14:07:20.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515113]
[2025-09-30 14:07:20.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515810]
[2025-09-30 14:07:30.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:30.6N +0530
        Min       = 2601 ns
        Max       = 1033680 ns
        Mean      = 3342 ns
        Mode      = bucket 3
        CPU Time  = 352282 us
        Wall Time = 26854 ms

[2025-09-30 14:07:30.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515586]
[2025-09-30 14:07:30.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515322]
[2025-09-30 14:07:30.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515724]
[2025-09-30 14:07:40.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:40.6N +0530
        Min       = 2427 ns
        Max       = 945484 ns
        Mean      = 5036 ns
        Mode      = bucket 1
        CPU Time  = 544043 us
        Wall Time = 25179 ms

[2025-09-30 14:07:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515372]
[2025-09-30 14:07:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515301]
[2025-09-30 14:07:40.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515285]
[2025-09-30 14:07:50.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:07:50.6N +0530
        Min       = 4677 ns
        Max       = 933601 ns
        Mean      = 3481 ns
        Mode      = bucket 4
        CPU Time  = 546980 us
        Wall Time = 25412 ms

[2025-09-30 14:07:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515183]
[2025-09-30 14:07:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515303]
[2025-09-30 14:07:50.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515192]
[2025-09-30 14:08:00.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:08:00.6N +0530
        Min       = 4965 ns
        Max       = 828582 ns
        Mean      = 5028 ns
        Mode      = bucket 4
        CPU Time  = 378594 us
        Wall Time = 28254 ms

[2025-09-30 14:08:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515779]
[2025-09-30 14:08:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515628]
[2025-09-30 14:08:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515804]
[2025-09-30 14:09:07.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:07.6N +0530
        Min       = 2911 ns
        Max       = 1196257 ns
        Mean      = 3155 ns
        Mode      = bucket 2
        CPU Time  = 343041 us
        Wall Time = 29654 ms

[2025-09-30 14:09:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515768]
[2025-09-30 14:09:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515013]
[2025-09-30 14:09:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515251]
[2025-09-30 14:09:17.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:17.6N +0530
        Min       = 3884 ns
        Max       = 970439 ns
        Mean      = 5368 ns
        Mode      = bucket 2
        CPU Time  = 340745 us
        Wall Time = 29207 ms

[2025-09-30 14:09:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515673]
[2025-09-30 14:09:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515008]
[2025-09-30 14:09:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515380]
[2025-09-30 14:09:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:27.6N +0530
        Min       = 1892 ns
        Max       = 877567 ns
        Mean      = 3325 ns
        Mode      = bucket 2
        CPU Time  = 378145 us
        Wall Time = 28508 ms

[2025-09-30 14:09:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515554]
[2025-09-30 14:09:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515688]
[2025-09-30 14:09:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515248]
[2025-09-30 14:09:37.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:37.6N +0530
        Min       = 1425 ns
        Max       = 1181062 ns
        Mean      = 3242 ns
        Mode      = bucket 4
        CPU Time  = 368552 us
        Wall Time = 27194 ms

[2025-09-30 14:09:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515082]
[2025-09-30 14:09:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515448]
[2025-09-30 14:09:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515147]
[2025-09-30 14:09:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:47.6N +0530
        Min       = 1302 ns
        Max       = 837153 ns
        Mean      = 3216 ns
        Mode      = bucket 1
        CPU Time  = 446934 us
        Wall Time = 25146 ms

[2025-09-30 14:09:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515318]
[2025-09-30 14:09:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515197]
[2025-09-30 14:09:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515229]
[2025-09-30 14:09:57.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:09:57.6N +0530
        Min       = 4642 ns
        Max       = 985110 ns
        Mean      = 5589 ns
        Mode      = bucket 4
        CPU Time  = 500954 us
        Wall Time = 25158 ms

[2025-09-30 14:09:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515880]
[2025-09-30 14:09:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515042]
[2025-09-30 14:09:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515814]
[2025-09-30 14:10:07.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:07.6N +0530
        Min       = 3534 ns
        Max       = 932055 ns
        Mean      = 5321 ns
        Mode      = bucket 3
        CPU Time  = 531240 us
        Wall Time = 28165 ms

[2025-09-30 14:10:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515155]
[2025-09-30 14:10:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515749]
[2025-09-30 14:10:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515239]
[2025-09-30 14:10:17.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:17.6N +0530
        Min       = 1139 ns
        Max       = 845741 ns
        Mean      = 4308 ns
        Mode      = bucket 5
        CPU Time  = 506072 us
        Wall Time = 28182 ms

[2025-09-30 14:10:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515258]
[2025-09-30 14:10:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515446]
[2025-09-30 14:10:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515425]
[2025-09-30 14:10:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:27.6N +0530
        Min       = 4378 ns
        Max       = 1139886 ns
        Mean      = 5951 ns
        Mode      = bucket 3
        CPU Time  = 344854 us
        Wall Time = 29693 ms

[2025-09-30 14:10:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515262]
[2025-09-30 14:10:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515267]
[2025-09-30 14:10:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515010]
[2025-09-30 14:10:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:38.6N +0530
        Min       = 1316 ns
        Max       = 997046 ns
        Mean      = 5069 ns
        Mode      = bucket 1
        CPU Time  = 456188 us
        Wall Time = 29958 ms

[2025-09-30 14:10:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515732]
[2025-09-30 14:10:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515779]
[2025-09-30 14:10:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515365]
[2025-09-30 14:10:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:48.6N +0530
        Min       = 2742 ns
        Max       = 860030 ns
        Mean      = 4226 ns
        Mode      = bucket 1
        CPU Time  = 529975 us
        Wall Time = 27862 ms

[2025-09-30 14:10:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515921]
[2025-09-30 14:10:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515698]
[2025-09-30 14:10:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515707]
[2025-09-30 14:10:58.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:10:58.6N +0530
        Min       = 2792 ns
        Max       = 1029941 ns
        Mean      = 3428 ns
        Mode      = bucket 5
        CPU Time  = 414049 us
        Wall Time = 27245 ms

[2025-09-30 14:10:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515562]
[2025-09-30 14:10:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515811]
[2025-09-30 14:10:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515900]
[2025-09-30 14:11:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:11:08.6N +0530
        Min       = 2061 ns
        Max       = 1017952 ns
        Mean      = 3832 ns
        Mode      = bucket 5
        CPU Time  = 457691 us
        Wall Time = 25090 ms

[2025-09-30 14:11:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515077]
[2025-09-30 14:11:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515607]
[2025-09-30 14:11:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515151]
[2025-09-30 14:11:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:11:18.6N +0530
        Min       = 4725 ns
        Max       = 952381 ns
        Mean      = 4373 ns
        Mode      = bucket 4
        CPU Time  = 499588 us
        Wall Time = 28993 ms

[2025-09-30 14:11:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515877]
[2025-09-30 14:11:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515973]
[2025-09-30 14:11:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515409]

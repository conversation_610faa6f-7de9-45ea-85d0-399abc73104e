[2025-09-30 14:34:55.862692 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515377]
[2025-09-30 14:34:55.862702 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515378]
[2025-09-30 14:34:55.862712 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515379]
[2025-09-30 14:34:55.862722 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515380]
[2025-09-30 14:34:55.862734 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515381]
[2025-09-30 14:34:55.862744 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515382]
[2025-09-30 14:35:12.124740 +0800][PRF][...../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:12.124749672 +0800
        Min       = 1877 ns
        Max       = 833798 ns
        Mean      = 3995 ns
        Mode      = bucket 2
        CPU Time  = 399546 us
        Wall Time = 27022 ms
        Max Time  = 2025-09-30 14:34:50.253648410 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      231 |     0.2% |         0.1%
           2-   4 us |    70943 |    70.9% |        53.8%
           4-   8 us |    25850 |    25.9% |        34.5%
           8-  16 us |     2548 |     2.5% |         6.9%
          16-  32 us |      364 |     0.4% |         1.8%
          32-  65 us |       21 |     0.0% |         0.2%
          65- 131 us |       17 |     0.0% |         0.4%
         131- 262 us |       14 |     0.0% |         0.8%
         262- 524 us |        8 |     0.0% |         0.8%
         524-1048 us |        4 |     0.0% |         0.7%

[2025-09-30 14:35:12.124868 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][D]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                185             <USER>   <GROUP> socket id<20> Send Target: <*************:17031> Interface: <***********:0>
                  0          6919   Multicast socket id<21> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
               1065             0   Multicast socket id<26> Send Target: <*************:17032> Interface: <***********:0>
                  0      97049920   Multicast socket id<7> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.00 Mbps
         Average RX Rate 28.73 Mbps

[2025-09-30 14:35:17.244776 +0800][PRF][...../src/base/src/profiler.cpp:139][H] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:17.244781921 +0800
        Min       = 1545 ns
        Max       = 1018968 ns
        Mean      = 5261 ns
        Mode      = bucket 2
        CPU Time  = 526191 us
        Wall Time = 27855 ms
        Max Time  = 2025-09-30 14:34:50.253834591 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      143 |     0.1% |         0.1%
           2-   4 us |    56868 |    56.9% |        32.0%
           4-   8 us |    29609 |    29.6% |        32.5%
           8-  16 us |    11288 |    11.3% |        23.4%
          16-  32 us |     1835 |     1.8% |         7.5%
          32-  65 us |      166 |     0.2% |         1.2%
          65- 131 us |       59 |     0.1% |         1.1%
         131- 262 us |       15 |     0.0% |         0.6%
         262- 524 us |       12 |     0.0% |         0.9%
         524-1048 us |        5 |     0.0% |         0.7%

[2025-09-30 14:35:17.244885 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][H]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                222             <USER>   <GROUP> socket id<31> Send Target: <*************:17031> Interface: <***********:0>
                  0          7400   Multicast socket id<32> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
              41137           185   Multicast socket id<33> Send Target: <*************:17032> Interface: <***********:0>
                  0      97349992   Multicast socket id<34> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.01 Mbps
         Average RX Rate 27.96 Mbps
[2025-09-30 13:53:45.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:53:45.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:53:45.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out []
[2025-09-30 13:57:00.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:57:00.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [2516]
[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [3425]
[2025-09-30 13:57:00.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4333]
[2025-09-30 13:57:10.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:57:10.6N +0530
        Min       =  ns
        Max       =  ns
        Mean      =  ns
        Mode      = bucket 
        CPU Time  =  us
        Wall Time =  ms

[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [2975]
[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [3884]
[2025-09-30 13:57:10.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4792]
[2025-09-30 13:59:16.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:16.6N +0530
        Min       = 2267 ns
        Max       = 865887 ns
        Mean      = 4378 ns
        Mode      = bucket 3
        CPU Time  = 303207 us
        Wall Time = 26036 ms

[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515425]
[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515393]
[2025-09-30 13:59:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515094]
[2025-09-30 13:59:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:27.6N +0530
        Min       = 2829 ns
        Max       = 836375 ns
        Mean      = 3489 ns
        Mode      = bucket 2
        CPU Time  = 449953 us
        Wall Time = 29762 ms

[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515654]
[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515573]
[2025-09-30 13:59:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515587]
[2025-09-30 13:59:37.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:37.6N +0530
        Min       = 4093 ns
        Max       = 1039852 ns
        Mean      = 3211 ns
        Mode      = bucket 2
        CPU Time  = 536133 us
        Wall Time = 25562 ms

[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515726]
[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515499]
[2025-09-30 13:59:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515820]
[2025-09-30 13:59:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:47.6N +0530
        Min       = 1172 ns
        Max       = 820387 ns
        Mean      = 5900 ns
        Mode      = bucket 1
        CPU Time  = 423499 us
        Wall Time = 28298 ms

[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515849]
[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515583]
[2025-09-30 13:59:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515592]
[2025-09-30 13:59:57.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 13:59:57.6N +0530
        Min       = 3061 ns
        Max       = 814474 ns
        Mean      = 5351 ns
        Mode      = bucket 1
        CPU Time  = 597656 us
        Wall Time = 29196 ms

[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515025]
[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515249]
[2025-09-30 13:59:57.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515386]
[2025-09-30 14:00:07.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:07.6N +0530
        Min       = 3722 ns
        Max       = 841887 ns
        Mean      = 4545 ns
        Mode      = bucket 4
        CPU Time  = 321999 us
        Wall Time = 29175 ms

[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515382]
[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515581]
[2025-09-30 14:00:07.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515412]
[2025-09-30 14:00:17.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:17.6N +0530
        Min       = 4298 ns
        Max       = 1109787 ns
        Mean      = 5850 ns
        Mode      = bucket 3
        CPU Time  = 472984 us
        Wall Time = 27143 ms

[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515624]
[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515069]
[2025-09-30 14:00:17.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515707]
[2025-09-30 14:00:27.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:27.6N +0530
        Min       = 2410 ns
        Max       = 1151325 ns
        Mean      = 4425 ns
        Mode      = bucket 2
        CPU Time  = 425228 us
        Wall Time = 26100 ms

[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515511]
[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515105]
[2025-09-30 14:00:27.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515222]
[2025-09-30 14:00:37.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:37.6N +0530
        Min       = 2832 ns
        Max       = 1031704 ns
        Mean      = 4059 ns
        Mode      = bucket 2
        CPU Time  = 595930 us
        Wall Time = 25760 ms

[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515025]
[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515144]
[2025-09-30 14:00:37.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515024]
[2025-09-30 14:00:47.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:47.6N +0530
        Min       = 1713 ns
        Max       = 1175885 ns
        Mean      = 3570 ns
        Mode      = bucket 1
        CPU Time  = 491542 us
        Wall Time = 27023 ms

[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515225]
[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515005]
[2025-09-30 14:00:47.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515166]
[2025-09-30 14:00:57.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:00:57.6N +0530
        Min       = 4531 ns
        Max       = 1079650 ns
        Mean      = 5990 ns
        Mode      = bucket 4
        CPU Time  = 515542 us
        Wall Time = 29445 ms

[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515860]
[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515601]
[2025-09-30 14:00:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515891]
[2025-09-30 14:01:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:08.6N +0530
        Min       = 2170 ns
        Max       = 1034747 ns
        Mean      = 4614 ns
        Mode      = bucket 5
        CPU Time  = 454181 us
        Wall Time = 28410 ms

[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515668]
[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515959]
[2025-09-30 14:01:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515822]
[2025-09-30 14:01:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:18.6N +0530
        Min       = 2718 ns
        Max       = 1179328 ns
        Mean      = 4024 ns
        Mode      = bucket 2
        CPU Time  = 415346 us
        Wall Time = 26729 ms

[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515749]
[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515353]
[2025-09-30 14:01:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515868]
[2025-09-30 14:01:28.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:28.6N +0530
        Min       = 3360 ns
        Max       = 815242 ns
        Mean      = 5826 ns
        Mode      = bucket 3
        CPU Time  = 471686 us
        Wall Time = 28816 ms

[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515004]
[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515875]
[2025-09-30 14:01:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515001]
[2025-09-30 14:01:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:38.6N +0530
        Min       = 2953 ns
        Max       = 1179711 ns
        Mean      = 3032 ns
        Mode      = bucket 2
        CPU Time  = 480560 us
        Wall Time = 29740 ms

[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515157]
[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515115]
[2025-09-30 14:01:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515689]
[2025-09-30 14:01:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:48.6N +0530
        Min       = 4678 ns
        Max       = 944850 ns
        Mean      = 5416 ns
        Mode      = bucket 1
        CPU Time  = 499782 us
        Wall Time = 26698 ms

[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515628]
[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515794]
[2025-09-30 14:01:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515771]
[2025-09-30 14:01:58.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:01:58.6N +0530
        Min       = 3367 ns
        Max       = 1085133 ns
        Mean      = 5779 ns
        Mode      = bucket 4
        CPU Time  = 332305 us
        Wall Time = 25455 ms

[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515248]
[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515411]
[2025-09-30 14:01:58.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515739]
[2025-09-30 14:02:08.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:08.6N +0530
        Min       = 1213 ns
        Max       = 800299 ns
        Mean      = 4323 ns
        Mode      = bucket 5
        CPU Time  = 516343 us
        Wall Time = 29203 ms

[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515344]
[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515107]
[2025-09-30 14:02:08.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515959]
[2025-09-30 14:02:18.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:18.6N +0530
        Min       = 1125 ns
        Max       = 1130676 ns
        Mean      = 3989 ns
        Mode      = bucket 1
        CPU Time  = 342394 us
        Wall Time = 29206 ms

[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515916]
[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515014]
[2025-09-30 14:02:18.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515983]
[2025-09-30 14:02:28.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:28.6N +0530
        Min       = 2764 ns
        Max       = 1185052 ns
        Mean      = 5922 ns
        Mode      = bucket 1
        CPU Time  = 559319 us
        Wall Time = 28346 ms

[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515151]
[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515429]
[2025-09-30 14:02:28.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515157]
[2025-09-30 14:02:38.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:38.6N +0530
        Min       = 3826 ns
        Max       = 1045168 ns
        Mean      = 4891 ns
        Mode      = bucket 1
        CPU Time  = 554065 us
        Wall Time = 28404 ms

[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515671]
[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515328]
[2025-09-30 14:02:38.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515621]
[2025-09-30 14:02:48.6N +0530][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:02:48.6N +0530
        Min       = 3871 ns
        Max       = 851163 ns
        Mean      = 4327 ns
        Mode      = bucket 2
        CPU Time  = 572567 us
        Wall Time = 29525 ms

[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515231]
[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515812]
[2025-09-30 14:02:48.6N +0530][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515692]

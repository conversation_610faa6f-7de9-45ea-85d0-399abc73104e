service:
  flush: 5
  storage.path: ./storage/
  parsers_file: parsers_multiline_fixed.yaml
  log_level: debug

pipeline:
  inputs:
    - name: tail
      path: ./logs/*.log
      ignore_older: 1d
      read_from_head: true
      db: ./db/fluent-bit-test.db
      buffer_chunk_size: 1M
      buffer_max_size: 128M
      storage.type: memory
      db.sync: normal
      tag: test_logs
      multiline.parser: multiline-prf-parser

  filters:
    - name: modify
      match: test_logs
      rename:
        - log text
      add:
        - applicationName test_fluent-bit
        - subsystemName test_multiline
        - computerName ${HOSTNAME}

  outputs:
    # Console output for debugging
    - name: stdout
      match: '*'
      format: json_lines

    # Coralogix output (update the API key)
    - name: http
      match: '*'
      host: ingress.ap1.coralogix.com
      port: 443
      uri: /logs/v1/singles
      format: json_lines
      tls: on
      header: Authorization Bearer cxtp_Qd7ke2dhCmpybJuGZxBn5EYeuXUjlT
      compress: gzip
      retry_limit: 10

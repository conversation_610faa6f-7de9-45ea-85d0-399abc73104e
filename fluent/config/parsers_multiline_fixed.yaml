multiline_parsers:
  - name: multiline-regex-fixed
    type: regex
    flush_timeout: 5000
    
    rules:
      # Start state: Match lines that begin with timestamp and [PRF]
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
        next_state: cont
      
      # Continuation state: Match any line that doesn't start with a timestamp
      # This will capture all the performance stats lines
      - state: cont
        regex: '^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]).*'
        next_state: cont

  # Alternative parser with more specific patterns
  - name: multiline-regex-specific
    type: regex
    flush_timeout: 5000
    
    rules:
      # Start state: Match PRF log lines
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*Performance Stats:'
        next_state: perf_stats
      
      # Performance stats continuation
      - state: perf_stats
        regex: '^(Performance counter|        |               Range |        ---|           \d|         Average|$)'
        next_state: perf_stats
      
      # Network stats section
      - state: perf_stats
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\d+ [+\-]\d{4}\]\[PRF\].*'
        next_state: network_stats
      
      - state: network_stats
        regex: '^(        tx-bytes|        ---|                \d|         Average|$)'
        next_state: network_stats

multiline_parsers:
  - name: multiline-regex-fixed
    type: regex
    flush_timeout: 5000

    rules:
      # Start state: Match lines that begin with timestamp and [PRF]
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[PRF\].*'
        next_state: cont

      # Continuation state: Match lines that don't start with [timestamp][LOG_LEVEL]
      # This excludes DEB, INF, ERR, WAR logs but includes performance stats
      - state: cont
        regex: '^(?!\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[).*'
        next_state: cont

  # Better parser - more precise pattern matching
  - name: multiline-prf-parser
    type: regex
    flush_timeout: 3000

    rules:
      # Start state: Match PRF Performance Stats lines specifically
      - state: start_state
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[PRF\].*Performance Stats:'
        next_state: perf_data

      # Continue with performance counter line
      - state: perf_data
        regex: '^Performance counter.*'
        next_state: perf_data

      # Continue with indented performance stats (spaces at start)
      - state: perf_data
        regex: '^        .*'
        next_state: perf_data

      # Continue with performance table headers and data
      - state: perf_data
        regex: '^               Range.*|^        -.*|^           \d.*|^         Average.*'
        next_state: perf_data

      # Continue with empty lines within the performance block
      - state: perf_data
        regex: '^$'
        next_state: perf_data

      # Handle network stats section (new PRF line)
      - state: perf_data
        regex: '^\[\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}\.\w+ [+\-]\d{4}\]\[PRF\].*'
        next_state: network_data

      # Network stats continuation
      - state: network_data
        regex: '^        (tx-bytes|---|.*\d).*|^         Average.*'
        next_state: network_data

      # Empty lines in network section
      - state: network_data
        regex: '^$'
        next_state: network_data

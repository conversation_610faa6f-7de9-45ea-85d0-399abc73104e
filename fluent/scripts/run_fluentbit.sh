#!/bin/bash

# Script to run Fluent Bit with the test configuration

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${GREEN}🚀 Starting Fluent Bit with test configuration...${NC}"
echo -e "${YELLOW}Project directory: $PROJECT_DIR${NC}"

# Change to project directory
cd "$PROJECT_DIR"

# Check if Fluent Bit is installed
if ! command -v fluent-bit &> /dev/null; then
    echo -e "${RED}[ERROR]${NC} Fluent Bit is not installed. Please run install_fluentbit.sh first."
    exit 1
fi

# Check if config file exists
if [ ! -f "config/fluent-bit-test.yaml" ]; then
    echo -e "${RED}[ERROR]${NC} Configuration file not found: config/fluent-bit-test.yaml"
    exit 1
fi

# Check if parser file exists
if [ ! -f "config/parsers_multiline_fixed.yaml" ]; then
    echo -e "${RED}[ERROR]${NC} Parser file not found: config/parsers_multiline_fixed.yaml"
    exit 1
fi

# Create directories if they don't exist
mkdir -p storage db logs

# Copy sample log if it doesn't exist
if [ ! -f "logs/test.log" ] && [ -f "../example.log.txt" ]; then
    echo -e "${YELLOW}[INFO]${NC} Copying sample log file..."
    cp "../example.log.txt" "logs/test.log"
fi

echo -e "${GREEN}[INFO]${NC} Configuration file: config/fluent-bit-test.yaml"
echo -e "${GREEN}[INFO]${NC} Parser file: config/parsers_multiline_fixed.yaml"
echo -e "${GREEN}[INFO]${NC} Log files: logs/*.log"
echo ""
echo -e "${YELLOW}[INFO]${NC} Press Ctrl+C to stop Fluent Bit"
echo ""

# Run Fluent Bit
fluent-bit --config=config/fluent-bit-test.yaml

#!/bin/bash

# Script to test multiline parsing with different configurations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🧪 Testing Multiline Parser Configurations${NC}"
echo -e "${YELLOW}Project directory: $PROJECT_DIR${NC}"

cd "$PROJECT_DIR"

# Function to test a specific parser
test_parser() {
    local parser_name=$1
    local config_file=$2
    
    echo -e "\n${GREEN}Testing parser: $parser_name${NC}"
    echo -e "${YELLOW}Config: $config_file${NC}"
    
    # Create a temporary config for testing
    cat > "config/test_$parser_name.yaml" << EOF
service:
  flush: 1
  storage.path: ./storage/
  parsers_file: parsers_multiline_fixed.yaml
  log_level: info

pipeline:
  inputs:
    - name: tail
      path: ./logs/test.log
      read_from_head: true
      db: ./db/test_$parser_name.db
      tag: test
      multiline.parser: $parser_name

  outputs:
    - name: stdout
      match: '*'
      format: json_lines
EOF

    echo -e "${BLUE}Running test for 10 seconds...${NC}"
    timeout 10s fluent-bit --config="config/test_$parser_name.yaml" || true
    
    # Clean up
    rm -f "config/test_$parser_name.yaml"
    rm -f "db/test_$parser_name.db"
}

# Ensure test log exists
if [ ! -f "logs/test.log" ]; then
    if [ -f "../example.log.txt" ]; then
        cp "../example.log.txt" "logs/test.log"
    else
        echo -e "${RED}[ERROR]${NC} No test log file found!"
        exit 1
    fi
fi

echo -e "\n${BLUE}Available parsers in parsers_multiline_fixed.yaml:${NC}"
grep -E "^\s*-\s*name:" config/parsers_multiline_fixed.yaml | sed 's/.*name: /  - /'

echo -e "\n${BLUE}Sample log content:${NC}"
head -20 logs/test.log

# Test the fixed parser
test_parser "multiline-regex-fixed" "config/parsers_multiline_fixed.yaml"

echo -e "\n${GREEN}✅ Testing completed!${NC}"
echo -e "${YELLOW}To run Fluent Bit continuously, use: ./scripts/run_fluentbit.sh${NC}"

#!/bin/bash

LOG_FILE="$1"
if [ -z "$LOG_FILE" ]; then
    LOG_FILE="../logs/test.log"
fi

echo "Generating test logs to: $LOG_FILE"
alias shuf="gshuf"
while true; do
    # Generate a PRF multiline log entry
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S.%6N %z')
    echo "[$TIMESTAMP][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:" >> "$LOG_FILE"
    echo "Performance counter for [HK2-MCAST reactor epoll event loop] at $TIMESTAMP" >> "$LOG_FILE"
    echo "        Min       = $(shuf -i 1000-5000 -n 1) ns" >> "$LOG_FILE"
    echo "        Max       = $(shuf -i 800000-1200000 -n 1) ns" >> "$LOG_FILE"
    echo "        Mean      = $(shuf -i 3000-6000 -n 1) ns" >> "$LOG_FILE"
    echo "        Mode      = bucket $(shuf -i 1-5 -n 1)" >> "$LOG_FILE"
    echo "        CPU Time  = $(shuf -i 300000-600000 -n 1) us" >> "$LOG_FILE"
    echo "        Wall Time = $(shuf -i 25000-30000 -n 1) ms" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
    
    # Generate some regular DEB log entries
    for i in {1..3}; do
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S.%6N %z')
        echo "[$TIMESTAMP][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [$(shuf -i 4515000-4516000 -n 1)]" >> "$LOG_FILE"
    done
    
    sleep 10
done

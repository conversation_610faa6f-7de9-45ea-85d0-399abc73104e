#!/bin/bash

# Script to verify the Fluent Bit test environment setup

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${BLUE}🔍 Verifying Fluent Bit Test Environment Setup${NC}"
echo -e "${YELLOW}Project directory: $PROJECT_DIR${NC}"

cd "$PROJECT_DIR"

# Function to check if a command exists
check_command() {
    if command -v "$1" &> /dev/null; then
        echo -e "${GREEN}✅ $1 is installed${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 is not installed${NC}"
        return 1
    fi
}

# Function to check if a file exists
check_file() {
    if [ -f "$1" ]; then
        echo -e "${GREEN}✅ $1 exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 not found${NC}"
        return 1
    fi
}

# Function to check if a directory exists
check_directory() {
    if [ -d "$1" ]; then
        echo -e "${GREEN}✅ $1 directory exists${NC}"
        return 0
    else
        echo -e "${RED}❌ $1 directory not found${NC}"
        return 1
    fi
}

echo -e "\n${BLUE}Checking Prerequisites:${NC}"
check_command "brew"
check_command "fluent-bit"

echo -e "\n${BLUE}Checking Directory Structure:${NC}"
check_directory "config"
check_directory "scripts"
check_directory "logs"
check_directory "storage"
check_directory "db"

echo -e "\n${BLUE}Checking Configuration Files:${NC}"
check_file "config/fluent-bit-test.yaml"
check_file "config/parsers_multiline_fixed.yaml"

echo -e "\n${BLUE}Checking Scripts:${NC}"
check_file "scripts/install_fluentbit.sh"
check_file "scripts/run_fluentbit.sh"
check_file "scripts/test_multiline.sh"
check_file "scripts/generate_logs.sh"

echo -e "\n${BLUE}Checking Test Data:${NC}"
check_file "logs/test.log"

echo -e "\n${BLUE}Checking Script Permissions:${NC}"
for script in scripts/*.sh; do
    if [ -x "$script" ]; then
        echo -e "${GREEN}✅ $script is executable${NC}"
    else
        echo -e "${RED}❌ $script is not executable${NC}"
        echo -e "${YELLOW}   Run: chmod +x $script${NC}"
    fi
done

echo -e "\n${BLUE}Configuration Validation:${NC}"

# Check if Coralogix API key is set
if grep -q "YOUR_CORALOGIX_API_KEY_HERE" config/fluent-bit-test.yaml; then
    echo -e "${YELLOW}⚠️  Coralogix API key not set in config/fluent-bit-test.yaml${NC}"
    echo -e "${YELLOW}   Please update the API key before sending logs to Coralogix${NC}"
else
    echo -e "${GREEN}✅ Coralogix API key appears to be set${NC}"
fi

# Test Fluent Bit configuration syntax
echo -e "\n${BLUE}Testing Fluent Bit Configuration:${NC}"
if fluent-bit --dry-run --config=config/fluent-bit-test.yaml &> /dev/null; then
    echo -e "${GREEN}✅ Fluent Bit configuration syntax is valid${NC}"
else
    echo -e "${RED}❌ Fluent Bit configuration has syntax errors${NC}"
    echo -e "${YELLOW}   Run: fluent-bit --dry-run --config=config/fluent-bit-test.yaml${NC}"
fi

# Show sample log content
echo -e "\n${BLUE}Sample Log Content (first 10 lines):${NC}"
if [ -f "logs/test.log" ]; then
    head -10 "logs/test.log" | sed 's/^/  /'
else
    echo -e "${RED}No test log file found${NC}"
fi

echo -e "\n${BLUE}Fluent Bit Version:${NC}"
fluent-bit --version | head -1

echo -e "\n${GREEN}🎉 Setup Verification Complete!${NC}"
echo -e "\n${YELLOW}Next Steps:${NC}"
echo -e "1. Update Coralogix API key in config/fluent-bit-test.yaml (if needed)"
echo -e "2. Run: ${GREEN}./scripts/test_multiline.sh${NC} to test multiline parsing"
echo -e "3. Run: ${GREEN}./scripts/run_fluentbit.sh${NC} to start Fluent Bit"
echo -e "4. Run: ${GREEN}./scripts/generate_logs.sh${NC} to generate test logs"

#!/bin/bash

# Fluent Bit Installation and Setup Script for macOS
# This script installs Fluent Bit, sets up configuration, and runs it as a service

set -e

echo "🚀 Starting Fluent Bit installation and setup..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

echo -e "${YELLOW}Project directory: $PROJECT_DIR${NC}"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Homebrew is installed
if ! command -v brew &> /dev/null; then
    print_error "Homebrew is not installed. Please install Homebrew first:"
    echo "  /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
    exit 1
fi

# Install Fluent Bit using Homebrew
print_status "Installing Fluent Bit via Homebrew..."
if brew list fluent-bit &> /dev/null; then
    print_warning "Fluent Bit is already installed. Upgrading..."
    brew upgrade fluent-bit
else
    brew install fluent-bit
fi

# Create necessary directories
print_status "Creating directories..."
mkdir -p "$PROJECT_DIR/storage"
mkdir -p "$PROJECT_DIR/logs"
mkdir -p "$PROJECT_DIR/db"

# Copy sample log file to test directory
print_status "Setting up test log file..."
cp "$PROJECT_DIR/../example.log.txt" "$PROJECT_DIR/logs/test.log"

# Create a script to generate continuous log entries for testing
cat > "$PROJECT_DIR/scripts/generate_logs.sh" << 'EOF'
#!/bin/bash

LOG_FILE="$1"
if [ -z "$LOG_FILE" ]; then
    LOG_FILE="../logs/test.log"
fi

echo "Generating test logs to: $LOG_FILE"

while true; do
    # Generate a PRF multiline log entry
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S.%6N %z')
    echo "[$TIMESTAMP][PRF][.../src/base/src/profiler.cpp:139][D] Performance Stats:" >> "$LOG_FILE"
    echo "Performance counter for [HK2-MCAST reactor epoll event loop] at $TIMESTAMP" >> "$LOG_FILE"
    echo "        Min       = $(shuf -i 1000-5000 -n 1) ns" >> "$LOG_FILE"
    echo "        Max       = $(shuf -i 800000-1200000 -n 1) ns" >> "$LOG_FILE"
    echo "        Mean      = $(shuf -i 3000-6000 -n 1) ns" >> "$LOG_FILE"
    echo "        Mode      = bucket $(shuf -i 1-5 -n 1)" >> "$LOG_FILE"
    echo "        CPU Time  = $(shuf -i 300000-600000 -n 1) us" >> "$LOG_FILE"
    echo "        Wall Time = $(shuf -i 25000-30000 -n 1) ms" >> "$LOG_FILE"
    echo "" >> "$LOG_FILE"
    
    # Generate some regular DEB log entries
    for i in {1..3}; do
        TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S.%6N %z')
        echo "[$TIMESTAMP][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [$(shuf -i 4515000-4516000 -n 1)]" >> "$LOG_FILE"
    done
    
    sleep 10
done
EOF

chmod +x "$PROJECT_DIR/scripts/generate_logs.sh"

print_status "✅ Installation completed!"
print_status "Next steps:"
echo "  1. Update the Coralogix API key in fluent-bit-test.yaml"
echo "  2. Run: ./scripts/run_fluentbit.sh"
echo "  3. In another terminal, run: ./scripts/generate_logs.sh to generate test logs"
echo "  4. Check logs with: tail -f logs/test.log"

[2025-09-30 14:34:55.862692 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515377]
[2025-09-30 14:34:55.862702 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515378]
[2025-09-30 14:34:55.862712 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515379]
[2025-09-30 14:34:55.862722 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515380]
[2025-09-30 14:34:55.862734 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SUBSCRIPTION] that has timed out [4515381]
[2025-09-30 14:34:55.862744 +0800][DEB][...c/messaging/src/context.cpp:1299][G] Pruning old incoming request on topic [SUP.THEO.RISK.REQUEST.SNAPSHOT] that has timed out [4515382]
[2025-09-30 14:35:12.124740 +0800][PRF][...../src/base/src/profiler.cpp:139][D] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:12.124749672 +0800
        Min       = 1877 ns
        Max       = 833798 ns
        Mean      = 3995 ns
        Mode      = bucket 2
        CPU Time  = 399546 us
        Wall Time = 27022 ms
        Max Time  = 2025-09-30 14:34:50.253648410 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      231 |     0.2% |         0.1%
           2-   4 us |    70943 |    70.9% |        53.8%
           4-   8 us |    25850 |    25.9% |        34.5%
           8-  16 us |     2548 |     2.5% |         6.9%
          16-  32 us |      364 |     0.4% |         1.8%
          32-  65 us |       21 |     0.0% |         0.2%
          65- 131 us |       17 |     0.0% |         0.4%
         131- 262 us |       14 |     0.0% |         0.8%
         262- 524 us |        8 |     0.0% |         0.8%
         524-1048 us |        4 |     0.0% |         0.7%

[2025-09-30 14:35:12.124868 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][D]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                185             <USER>   <GROUP> socket id<20> Send Target: <*************:17031> Interface: <***********:0>
                  0          6919   Multicast socket id<21> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
               1065             0   Multicast socket id<26> Send Target: <*************:17032> Interface: <***********:0>
                  0      97049920   Multicast socket id<7> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.00 Mbps
         Average RX Rate 28.73 Mbps

[2025-09-30 14:35:17.244776 +0800][PRF][...../src/base/src/profiler.cpp:139][H] Performance Stats:
Performance counter for [HK2-MCAST reactor epoll event loop] at 2025-09-30 14:35:17.244781921 +0800
        Min       = 1545 ns
        Max       = 1018968 ns
        Mean      = 5261 ns
        Mode      = bucket 2
        CPU Time  = 526191 us
        Wall Time = 27855 ms
        Max Time  = 2025-09-30 14:34:50.253834591 +0800

               Range |    Count |  Count % | Time Spent %
        -------------+----------+----------+--------------
           1-   2 us |      143 |     0.1% |         0.1%
           2-   4 us |    56868 |    56.9% |        32.0%
           4-   8 us |    29609 |    29.6% |        32.5%
           8-  16 us |    11288 |    11.3% |        23.4%
          16-  32 us |     1835 |     1.8% |         7.5%
          32-  65 us |      166 |     0.2% |         1.2%
          65- 131 us |       59 |     0.1% |         1.1%
         131- 262 us |       15 |     0.0% |         0.6%
         262- 524 us |       12 |     0.0% |         0.9%
         524-1048 us |        5 |     0.0% |         0.7%

[2025-09-30 14:35:17.244885 +0800][PRF][...c/base/src/reactor_epoll.cpp:635][H]
        tx-bytes      rx-bytes      Description
        -----------   -----------   ----------------------------------------------------------------------------------------------------------
                222             <USER>   <GROUP> socket id<31> Send Target: <*************:17031> Interface: <***********:0>
                  0          7400   Multicast socket id<32> Join Group: <*************:0> Send Target: <*************:17031> Interface: <***********:17031>
              41137           185   Multicast socket id<33> Send Target: <*************:17032> Interface: <***********:0>
                  0      97349992   Multicast socket id<34> Join Group: <*************:0> Send Target: <*************:17032> Interface: <***********:17032>

         Average TX Rate 0.01 Mbps
         Average RX Rate 27.96 Mbps

service:
  flush: 5
  storage.path: /app/qrt_appmon/fluent-bit/flb-storage/
  parsers_file: parsers_multiline.yaml

pipeline:
    inputs:
      - name: tail
        path: /app/qrt_sup/qcore/logs/*.log
        exclude_path: /app/qrt_sup/qcore/logs/orderrouterd-*.log, /app/qrt_sup/qcore/logs/*broker*.log, /app/qrt_sup/qcore/logs/*orderd*.log, /app/qrt_sup/qcore/logs/*positiond*.log, /app/qrt_sup/qcore/logs/*securityd*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore

      - name: tail
        path: /app/qrt_sup/qcore/logs/orderrouterd-*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore_orderrouter

      - name: tail
        path: /app/qrt_sup/qcore/logs/*broker*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore_broker
        multiline.parser: multiline-regex-test


      - name: tail
        path: /app/qrt_sup/qcore/logs/*orderd*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore_orderd

      - name: tail
        path: /app/qrt_sup/qcore/logs/*positiond*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore_positiond

      - name: tail
        path: /app/qrt_sup/qcore/logs/*securityd*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: qcore_securityd


      - name: tail
        path: /app/qrt_sup/xi/xi_simhk/log/*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: xi

      - name: tail
        path: /app/qrt_sup/xi/xi_simhk/log/*Raw.log.*
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: xi_raw

      - name: tail
        path: /app/qrt_sup/marliteQcore/log/*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: marlite

      - name: tail
        path: /app/qrt_prod/pi/log/*.log
        ignore_older: 1d
        read_from_head: true
        db: /app/qrt_appmon/fluent-bit/fluent-bit-appmon.db
        buffer_chunk_size: 1M
        buffer_max_size: 128M
        storage.type: memory
        db.sync: normal
        tag: pi

      - name: node_exporter_metrics
        scrape_interval: 10
        metrics: cpu,cpufreq,meminfo,diskstats,filesystem,uname,stat,time,loadavg,vmstat,netdev,filefd
        tag: node_metrics

    outputs:
      - name: http
        match: '*'
        host: ingress.private.eu1.coralogix.com
        port: 443
        uri: /logs/v1/singles
        format: json_lines
        tls: on
        header: Authorization Bearer cxtp_aKzeNQSoIXy4PlO7qhIodr8Kva4fix
        compress: gzip
        retry_limit: 10


      - name: prometheus_remote_write
        match: node_metrics
        host: ingress.private.eu1.coralogix.com
        port: 443
        uri: /prometheus/v1
        tls: on
        header: Authorization Bearer cxtp_aKzeNQSoIXy4PlO7qhIodr8Kva4fix
        add_label: applicationName fluentbit_metrics
        add_label: subsystemName   ${HOSTNAME}


    filters:
      - name: modify
        match: qcore
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_all
          - computerName ${HOSTNAME}

      - name: modify
        match: qcore_orderrouter
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_orderrouter_fb
          - computerName ${HOSTNAME}

      - name: modify
        match: qcore_broker
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_broker_fb
          - computerName ${HOSTNAME}


      - name: modify
        match: qcore_orderd
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_orderd_fb
          - computerName ${HOSTNAME}

      - name: modify
        match: qcore_positiond
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_positiond_fb
          - computerName ${HOSTNAME}

      - name: modify
        match: qcore_securityd
        rename:
          - log text
        add:
          - applicationName qcore_fluent-bit
          - subsystemName qcore_securityd_fb
          - computerName ${HOSTNAME}

      - name: modify
        match: xi
        rename:
          - log text
        add:
          - applicationName xi_fluent-bit
          - subsystemName xi_all
          - computerName ${HOSTNAME}

      - name: modify
        match: xi
        rename:
          - log text
        add:
          - applicationName xi_fluent-bit
          - subsystemName xi_raw_fb
          - computerName ${HOSTNAME}

      - name: modify
        match: marlite
        rename:
          - log text
        add:
          - applicationName marlite_fluent-bit
          - subsystemName marlite_all
          - computerName ${HOSTNAME}

      - name: modify
        match: pi
        rename:
          - log text
        add:
          - applicationName pi_fluent-bit
          - subsystemName pi_all
          - computerName ${HOSTNAME}